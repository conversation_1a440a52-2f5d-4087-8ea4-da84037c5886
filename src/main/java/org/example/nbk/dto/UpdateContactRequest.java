package org.example.nbk.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateContactRequest {

    @NotNull
    @Valid
    @JsonProperty("Contact") // 明确映射到JSON中的"Contact"字段
    private ContactDto contact;
} 