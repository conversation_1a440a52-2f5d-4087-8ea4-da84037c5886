package org.example.nbk.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginResponseDto {
    private Integer id;
    private String fullName;
    private String userName;
    // Password intentionally omitted for security
    private String token;
    private Boolean isAdmin;
    private Boolean isMobileApp; // Reflecting the input flag for now
    private Integer companyID;
} 