package org.example.nbk.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ContactDto {
    private Integer id;
    private String name;
    private String contactNo;
    private String email;
    private String companyName;
    @JsonIgnore
    private Integer companyId;
} 