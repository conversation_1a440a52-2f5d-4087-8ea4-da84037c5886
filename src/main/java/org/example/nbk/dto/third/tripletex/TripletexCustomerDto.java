package org.example.nbk.dto.third.tripletex;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.example.nbk.dto.third.tripletex.AddressDto;
import org.example.nbk.dto.third.tripletex.EmployeeDto;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TripletexCustomerDto {
    private Integer id;
    private String name;
    private String email;
    private String invoiceEmail;
    private String phoneNumberMobile;
    private EmployeeDto accountManager;
    private String organizationNumber; // Mapped from C#'s use of vatNumber
    private String customerNumber;
    private String phoneNumber;
    private AddressDto invoiceAddress;
    private String invoiceSendMethod;
}