package org.example.nbk.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.nbk.entity.CompanyWrapper;

/**
 * 请求响应实体类
 * 用于表示API请求的通用响应格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RequestResponse {
    
    /**
     * 操作是否成功
     */
    private Boolean isSuccess;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private Object data;
    
    @JsonIgnore
    private Integer userProfileID;
    
    @JsonIgnore
    private boolean isAdminUser = false;
    
    @JsonIgnore
    private CompanyWrapper dataCompany = new CompanyWrapper();

    /**
     * 公司信息内部类
     * 用于支持公司相关数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanyInfo {
        private Integer companyID;
        private String companyName;
        private String organizationalNumber;
        private String address;
        private String ownerName;
        private Integer postCode;
        private Integer cityID;
        private String emailAddress;
        private String telephone;
        private String mobile;
    }

    /**
     * 构造一个两参数的响应对象
     * 
     * @param isSuccess 是否成功
     * @param message 消息
     */
    public RequestResponse(Boolean isSuccess, String message) {
        this.isSuccess = isSuccess;
        this.message = message;
        this.data = null;
    }

    /**
     * 构造一个三参数的响应对象
     * 
     * @param isSuccess 是否成功
     * @param message 消息
     * @param data 数据
     */
    public RequestResponse(Boolean isSuccess, String message, Object data) {
        this.isSuccess = isSuccess;
        this.message = message;
        this.data = data;
    }

    /**
     * 判断操作是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(isSuccess);
    }
    
    /**
     * 设置操作是否成功
     * 
     * @param success 是否成功
     */
    public void setSuccess(boolean success) {
        this.isSuccess = success;
    }

    /**
     * 构造一个成功响应
     * 
     * @param message 成功消息
     * @return 成功响应对象
     */
    public static RequestResponse success(String message) {
        return new RequestResponse(true, message, null);
    }
    
    /**
     * 构造一个成功响应，带数据
     * 
     * @param message 成功消息
     * @param data 响应数据
     * @return 成功响应对象
     */
    public static RequestResponse success(String message, Object data) {
        return new RequestResponse(true, message, data);
    }
    
    /**
     * 构造一个失败响应
     * 
     * @param message 失败消息
     * @return 失败响应对象
     */
    public static RequestResponse failure(String message) {
        return new RequestResponse(false, message, null);
    }
} 