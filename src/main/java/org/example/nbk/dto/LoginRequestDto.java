package org.example.nbk.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
// import jakarta.validation.constraints.NotNull; // Keep commented as per previous user change
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class LoginRequestDto {
    @JsonProperty("UserName")
    @NotBlank(message = "UserName cannot be blank")
    private String userName;

    @JsonProperty("Password")
    @NotBlank(message = "Password cannot be blank")
    private String password;

    // Assuming frontend might also send IsMobileApp in PascalCase
    @JsonProperty("IsMobileApp")
    //@NotNull(message = "isMobileApp flag must be provided") // Keep commented
    private Boolean isMobileApp = false; // Assumed to be part of the request
} 