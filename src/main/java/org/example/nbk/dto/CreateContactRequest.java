package org.example.nbk.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateContactRequest {

    @NotNull
    @Valid
    @JsonProperty("Contact") // Explicitly map to "Contact" in JSON
    private ContactDto contact; // Changed field name to lowercase
} 