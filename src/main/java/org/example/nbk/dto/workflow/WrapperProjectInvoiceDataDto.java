package org.example.nbk.dto.workflow;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WrapperProjectInvoiceDataDto {

    // This field will hold the actual invoice data.
    // In C#, this was ProjectInvoiceDataENT. We'll create a placeholder DTO for it.
    private ProjectInvoiceDataDto projectInvoiceDataENT; 
} 