package org.example.nbk.dto.workflow;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectInvoiceDataDto {
    // A placeholder DTO for invoice data.
    // In a real implementation, this would contain fields from Tripletex.
    private Integer projectId;
    private Integer workflowId;
    private Integer workflowStepId;
    private String invoiceDetails; // Example field
    private Double amount; // Example field
} 