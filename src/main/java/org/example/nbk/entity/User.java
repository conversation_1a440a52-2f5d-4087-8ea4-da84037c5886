package org.example.nbk.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;

@Data
@NoArgsConstructor
@Entity
@Table(name = "Users", schema = "nbkUser") // Specify schema as requested
public class User implements UserDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY) // Assuming ID is an identity column
    @Column(name = "ID")
    private Integer id;

    @Column(name = "FullName", length = 250)
    private String fullName;

    @Column(name = "UserName", length = 50)
    private String userName;

    @Column(name = "Designation", length = 250)
    private String designation;

    @Column(name = "Email", length = 500)
    private String email;

    @Column(name = "Password", length = 50)
    private String password;

    @Column(name = "UserTypeID")
    private Integer userTypeID;

    @Lob // For nvarchar(max)
    @Column(name = "Address", columnDefinition = "nvarchar(max)")
    private String address;

    @Column(name = "ContactNo", length = 250)
    private String contactNo;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "Picture", length = 250)
    private String picture;

    @Lob // For nvarchar(max)
    @Column(name = "Token", columnDefinition = "nvarchar(max)")
    private String token;

    @Column(name = "TokenValidFrom")
    private LocalDateTime tokenValidFrom;

    @Column(name = "TokenValidTo")
    private LocalDateTime tokenValidTo;

    @Column(name = "ContactId")
    private Integer contactId;

    @Column(name = "IsAdmin")
    private Boolean isAdmin;

    @Column(name = "CompanyID")
    private Integer companyID;

    @Column(name = "IsSystemOwner")
    private Boolean isSystemOwner;

    // UserDetails interface implementation
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // Based on isAdmin flag, assign ROLE_ADMIN or ROLE_USER
        String role = Boolean.TRUE.equals(isAdmin) ? "ROLE_ADMIN" : "ROLE_USER";
        return Collections.singletonList(new SimpleGrantedAuthority(role));
    }

    @Override
    public String getUsername() {
        return userName; // Using userName as the username
    }

    @Override
    public boolean isAccountNonExpired() {
        return true; // Account never expires
    }

    @Override
    public boolean isAccountNonLocked() {
        return true; // Account is never locked
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true; // Credentials never expire
    }

    @Override
    public boolean isEnabled() {
        return Boolean.TRUE.equals(isActive); // Use isActive flag to determine if account is enabled
    }
} 