package org.example.nbk.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 公司包装类，用于响应中包含公司信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanyWrapper {
    private Integer companyId;
    private String companyName;
    private String description;
    private String address;
    
    // 添加与原C#代码兼容的字段
    private Integer companyID;
    private Boolean isSystemOwner;
    
    /**
     * 获取公司ID（兼容原C#代码的大写ID）
     * @return 公司ID
     */
    public Integer getCompanyID() {
        // 如果companyID为null但companyId不为null，则返回companyId
        return companyID != null ? companyID : companyId;
    }
    
    /**
     * 设置公司ID（兼容原C#代码的大写ID）
     * @param companyID 公司ID
     */
    public void setCompanyID(Integer companyID) {
        this.companyID = companyID;
        // 同时设置小写id以保持一致性
        this.companyId = companyID;
    }
    
    /**
     * 判断是否为系统所有者
     * @return 是否为系统所有者
     */
    public Boolean getSystemOwner() {
        return isSystemOwner;
    }
    
    /**
     * 设置是否为系统所有者
     * @param systemOwner 是否为系统所有者
     */
    public void setSystemOwner(Boolean systemOwner) {
        this.isSystemOwner = systemOwner;
    }
}