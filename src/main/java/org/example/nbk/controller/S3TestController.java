package org.example.nbk.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.nbk.config.StaticDetails;
import org.example.nbk.dto.S3TestResponse;
import org.example.nbk.service.S3Service;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
@Slf4j
public class S3TestController {

    private final S3Service s3Service;

    @GetMapping("/s3-check")
    public ResponseEntity<S3TestResponse> checkS3Connection(
            @RequestParam(required = false, defaultValue = "1") Integer companyId) {
        
        try {
            String bucketFolder = s3Service.getCompanyS3Folder(companyId);
            InputStream fileStream = s3Service.getFileFromS3(
                    bucketFolder,
                    StaticDetails.S3_BUCKET_FOLDER_FOR_HTML_TO_PDF_HEADER_IMAGE_WHITE);
            
            if (fileStream != null) {
                return ResponseEntity.ok(new S3TestResponse(
                        true, 
                        "Successfully connected to S3 and found watermark image", 
                        Map.of("bucketFolder", bucketFolder)
                ));
            } else {
                return ResponseEntity.ok(new S3TestResponse(
                        false, 
                        "Connected to S3 but watermark image not found", 
                        Map.of("bucketFolder", bucketFolder)
                ));
            }
        } catch (Exception e) {
            return ResponseEntity.ok(new S3TestResponse(
                    false, 
                    "Failed to connect to S3: " + e.getMessage(), 
                    null
            ));
        }
    }

    @PostMapping("/upload")
    public ResponseEntity<S3TestResponse> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(required = false, defaultValue = "1") Integer companyId) {
        
        try {
            String bucketFolder = s3Service.getCompanyS3Folder(companyId);
            String fileName = file.getOriginalFilename();
            String uploadResult = s3Service.uploadFile(bucketFolder, file, fileName);
            
            if ("Success".equals(uploadResult)) {
                String bucketName = s3Service.getCompanyBucketName(companyId);
                String urlStaticPart = s3Service.getCompanyS3UrlStaticPart(companyId);
                String fileUrl = s3Service.createPublicUrl(bucketName, urlStaticPart, bucketFolder, fileName);
                
                return ResponseEntity.ok(new S3TestResponse(
                        true, 
                        "File uploaded successfully", 
                        Map.of("fileUrl", fileUrl)
                ));
            } else {
                return ResponseEntity.ok(new S3TestResponse(
                        false, 
                        "Failed to upload file: " + uploadResult, 
                        null
                ));
            }
        } catch (Exception e) {
            return ResponseEntity.ok(new S3TestResponse(
                    false, 
                    "Error during file upload: " + e.getMessage(), 
                    null
            ));
        }
    }

    @GetMapping("/paths")
    public ResponseEntity<S3TestResponse> getPaths(
            @RequestParam(required = false, defaultValue = "1") Integer companyId) {
        
        try {
            Map<String, String> paths = s3Service.getAllS3Paths(companyId);
            
            return ResponseEntity.ok(new S3TestResponse(
                    true, 
                    "S3 paths retrieved successfully", 
                    paths
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(new S3TestResponse(
                    false, 
                    "Error retrieving S3 paths: " + e.getMessage(), 
                    null
            ));
        }
    }

    @PostMapping("/create-folder")
    public ResponseEntity<S3TestResponse> createFolder(
            @RequestParam String folderPath) {
        
        try {
            boolean success = s3Service.createFolder(folderPath);
            
            return ResponseEntity.ok(new S3TestResponse(
                    success, 
                    success ? "Folder created successfully" : "Failed to create folder", 
                    Map.of("folderPath", folderPath)
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(new S3TestResponse(
                    false, 
                    "Error creating folder: " + e.getMessage(), 
                    null
            ));
        }
    }

    @PostMapping("/init-company")
    public ResponseEntity<S3TestResponse> initializeCompanyFolders(
            @RequestParam(required = false, defaultValue = "1") Integer companyId,
            @RequestParam(required = false) String resourceFile) {
        
        try {
            boolean success = s3Service.initializeCompanyFolders(companyId, resourceFile);
            
            return ResponseEntity.ok(new S3TestResponse(
                    success, 
                    success ? "Company folders initialized successfully" : "Failed to initialize company folders",
                    Map.of("companyId", companyId)
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(new S3TestResponse(
                    false, 
                    "Error initializing company folders: " + e.getMessage(),
                    null
            ));
        }
    }

    @GetMapping("/folder-exists")
    public ResponseEntity<S3TestResponse> folderExists(
            @RequestParam String folderPath) {
        
        try {
            boolean exists = s3Service.folderExists(folderPath);
            
            return ResponseEntity.ok(new S3TestResponse(
                    true, 
                    exists ? "Folder exists" : "Folder does not exist", 
                    Map.of("folderExists", exists)
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(new S3TestResponse(
                    false, 
                    "Error checking folder existence: " + e.getMessage(), 
                    null
            ));
        }
    }
} 