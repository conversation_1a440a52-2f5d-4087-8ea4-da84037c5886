package org.example.nbk.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.nbk.dto.UserDto;
import org.example.nbk.entity.User;
import org.example.nbk.service.UserService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户控制器，处理用户相关API
 */
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {

    private final UserService userService;

    /**
     * 获取所有用户
     * 对应C#版本的 GetAll() 方法
     * @param currentUser 当前登录用户
     * @return 所有用户列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")  // 只允许管理员访问
    public ResponseEntity<Map<String, List<UserDto>>> getAllUsers(@AuthenticationPrincipal User currentUser) {
        log.info("Received request to get all users by user: {}", currentUser.getUsername());

        List<User> users = userService.getAllUsers();
        
        // 将User实体转换为UserDto
        List<UserDto> userDtos = users.stream()
                .map(UserDto::fromEntity)
                .collect(Collectors.toList());
        
        // 将结果包装在一个Map中，与其他API保持一致的响应格式
        Map<String, List<UserDto>> response = new HashMap<>();
        response.put("users", userDtos);

        log.info("Successfully fetched {} users", userDtos.size());
        return ResponseEntity.ok(response);
    }
} 