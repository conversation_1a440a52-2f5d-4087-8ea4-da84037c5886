package org.example.nbk.controller;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.example.nbk.dto.LoginRequestDto;
import org.example.nbk.dto.LoginResponseDto;
import org.example.nbk.service.UserService; // Assuming authenticate is in UserService
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/users") // Base path for user related auth
public class AuthController {

    private final UserService userService; // Or AuthService if created separately

    @Autowired
    public AuthController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping("/Authenticate")
    public ResponseEntity<LoginResponseDto> authenticateUser(@Valid @RequestBody LoginRequestDto loginRequest) {
        log.info("Received authentication request for user: {}", loginRequest.getUserName());
        LoginResponseDto loginResponse = userService.authenticate(loginRequest);
        log.info("Authentication successful for user: {}", loginRequest.getUserName());
        return ResponseEntity.ok(loginResponse);
        // AuthenticationException will be handled by GlobalExceptionHandler
    }
} 