package org.example.nbk.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.nbk.entity.*;
import org.example.nbk.repository.DocTypeRepository;
import org.example.nbk.service.AuthorizeService;
import org.example.nbk.service.DocTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/api/DocType")
public class DocTypeController {

    private final DocTypeService docTypeService;
    private final DocTypeRepository docTypeRepository;
    private final AuthorizeService authorizeService;

    @Autowired
    public DocTypeController(
            DocTypeService docTypeService,
            DocTypeRepository docTypeRepository,
            AuthorizeService authorizeService) {
        this.docTypeService = docTypeService;
        this.docTypeRepository = docTypeRepository;
        this.authorizeService = authorizeService;
    }

    @GetMapping("/GetDocType")
    public ResponseEntity<?> getDocType(@RequestParam int DocTypeID, HttpServletRequest request) {
        // Validate Token
        RequestResponse isAuthorized = authorizeService.requestTokenAuth(request);
        if (!isAuthorized.isSuccess()) {
            log.warn("Authentication failed for GetDocType: {}", isAuthorized.getMessage());
            return ResponseEntity.badRequest().body(isAuthorized);
        }
        
        WrapperDocType data = docTypeService.getSingleDocType(DocTypeID);
        return ResponseEntity.ok(data);
    }

    @PutMapping("/UpdateDocType")
    public ResponseEntity<?> updateDocType(@Valid @RequestBody WrapperDocType userParam, HttpServletRequest request) {
        // Validate Token
        RequestResponse isAuthorized = authorizeService.requestTokenAuth(request);
        if (!isAuthorized.isSuccess()) {
            log.warn("Authentication failed for UpdateDocType: {}", isAuthorized.getMessage());
            return ResponseEntity.badRequest().body(isAuthorized);
        }
        
        WrapperDocType data = docTypeService.updateSingleDocType(userParam.getDocType());
        return ResponseEntity.ok(data);
    }

    @DeleteMapping("/DeleteDocType")
    public ResponseEntity<?> deleteDocType(@RequestParam int DocTypeID, HttpServletRequest request) {
        // Validate Token
        RequestResponse isAuthorized = authorizeService.requestTokenAuth(request);
        if (!isAuthorized.isSuccess()) {
            log.warn("Authentication failed for DeleteDocType: {}", isAuthorized.getMessage());
            return ResponseEntity.badRequest().body(isAuthorized);
        }
        
        RequestResponse response = docTypeService.deleteSingleDocType(DocTypeID);
        if (!response.isSuccess()) {
            log.warn("Failed to delete document type: {}", response.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
        
        return ResponseEntity.ok(response);
    }

    @PostMapping("/CreatDocType")
    public ResponseEntity<?> creatDocType(@Valid @RequestBody WrapperDocType userParam, HttpServletRequest request) {
        // Validate Token
        RequestResponse isAuthorized = authorizeService.requestTokenAuth(request);
        if (!isAuthorized.isSuccess()) {
            log.warn("Authentication failed for CreatDocType: {}", isAuthorized.getMessage());
            return ResponseEntity.badRequest().body(isAuthorized);
        }
        
        // Create a service instance with company data
        DocTypeService serviceWithCompany = new DocTypeService(docTypeRepository, isAuthorized.getDataCompany());
        WrapperDocType data = serviceWithCompany.createSingleDocType(userParam.getDocType());
        
        return ResponseEntity.ok(data);
    }

    @GetMapping("/GetAllDocType")
    public ResponseEntity<?> getAllDocType(HttpServletRequest request) {
        // Validate Token
        RequestResponse isAuthorized = authorizeService.requestTokenAuth(request);
        if (!isAuthorized.isSuccess()) {
            log.warn("Authentication failed for GetAllDocType: {}", isAuthorized.getMessage());
            return ResponseEntity.badRequest().body(isAuthorized);
        }
        
        // Create a service instance with company data
        DocTypeService serviceWithCompany = new DocTypeService(docTypeRepository, isAuthorized.getDataCompany());
        WrapperMultiDocTypes data = serviceWithCompany.getAllDocType();
        
        return ResponseEntity.ok(data);
    }
} 