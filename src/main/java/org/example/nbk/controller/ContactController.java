package org.example.nbk.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.nbk.dto.ContactDto;
import org.example.nbk.dto.CreateContactRequest;
import org.example.nbk.dto.CreateContactResponse;
import org.example.nbk.dto.DeleteContactResponseDto;
import org.example.nbk.dto.UpdateContactRequest;
import org.example.nbk.entity.User;
import org.example.nbk.service.ContactService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/Contact") // Matches the requested base path
@RequiredArgsConstructor
@Slf4j
public class ContactController {

    private final ContactService contactService;

    @PostMapping("/CreatContact") // Matches the requested specific endpoint path
    @PreAuthorize("isAuthenticated()") // Secure this endpoint, requires JWT validation via Spring Security
    public ResponseEntity<CreateContactResponse> createContact(@Valid @RequestBody CreateContactRequest request, @AuthenticationPrincipal User user) {
        log.info("Received request to create contact: Name = {}", request.getContact().getName());
        Integer companyId = null;
        if (user != null) {
            log.info("Request made by user: {}, CompanyID: {}", user.getUsername(), user.getCompanyID());
            companyId = user.getCompanyID();
        } else {
            log.warn("User details not available in request for contact creation.");
            // Depending on requirements, might throw an exception if companyId is mandatory
        }

        // Extract the core ContactDto from the request wrapper
        ContactDto inputDto = request.getContact();

        // Call the service to create the contact, passing companyId as a separate argument
        ContactDto createdContactDto = contactService.createContact(inputDto, companyId);

        // Wrap the result DTO into the response structure
        CreateContactResponse response = new CreateContactResponse(createdContactDto);

        log.info("Successfully created contact with ID: {}", createdContactDto.getId());
        return ResponseEntity.ok(response);
        // Potential exceptions (e.g., validation, database) are expected to be handled
        // by a global exception handler (@ControllerAdvice)
    }

    @GetMapping("/GetAllContact")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Map<String, List<ContactDto>>> getAllContacts(@AuthenticationPrincipal User user) {
        log.info("Received request to get all contacts.");
        Integer companyId = null;
        if (user != null) {
            log.info("Request made by user: {}, CompanyID: {}", user.getUsername(), user.getCompanyID());
            companyId = user.getCompanyID();
        } else {
            // Should not happen if @PreAuthorize("isAuthenticated()") is effective
            log.warn("User details not available in request for getting all contacts.");
            // Optionally, return an unauthorized error or an empty list based on requirements
            // For now, proceeding will likely result in an error or empty list if companyId is strictly needed by the service
        }

        if (companyId == null) {
            // Handle cases where companyId could not be determined (e.g. user is null or user has no companyId)
            // Returning an empty list or an appropriate error response
            log.warn("CompanyID is null, returning empty list of contacts.");
            Map<String, List<ContactDto>> emptyResponse = new HashMap<>();
            emptyResponse.put("multiContact", List.of());
            return ResponseEntity.ok(emptyResponse); // Or consider ResponseEntity.status(HttpStatus.BAD_REQUEST).build(); if companyId is mandatory
        }

        List<ContactDto> contactDtos = contactService.getAllContactsByCompanyId(companyId);

        Map<String, List<ContactDto>> response = new HashMap<>();
        response.put("multiContact", contactDtos);

        log.info("Successfully retrieved {} contacts for CompanyID: {}", contactDtos.size(), companyId);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/UpdateContact")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ContactDto> updateContact(@Valid @RequestBody UpdateContactRequest request, @AuthenticationPrincipal User user) {
        log.debug("Received request to update contact with ID: {}", request.getContact().getId());
        Integer requestingUserCompanyId = null;
        if (user != null) {
            log.info("Request made by user: {}, CompanyID: {}", user.getUsername(), user.getCompanyID());
            requestingUserCompanyId = user.getCompanyID();
        } else {
            // This case should ideally be prevented by @PreAuthorize, but as a safeguard:
            log.warn("User details not available in authenticated request for contact update.");
            // Depending on strictness, could throw an AccessDeniedException or similar here.
            // For now, service layer will handle if requestingUserCompanyId is null (though it might be better to block earlier).
        }

        if (requestingUserCompanyId == null) {
             log.error("Update CANCELED: Could not determine CompanyID for the authenticated user.");
             // Or throw new AccessDeniedException("User CompanyID could not be determined.");
             // Returning a 403 or 400 Bad Request might be appropriate.
             // For now, this path leads to a likely AccessDeniedException in the service if it expects a non-null companyId for comparison.
             // Let's ensure the service method can handle a null requestingUserCompanyId if we don't block here.
             // Based on current service impl, it will throw AccessDeniedException if existingContact.getCompanyId() is not null
             // and requestingUserCompanyId is null, due to .equals() on a null object if existingContact.getCompanyId() is null.
             // To be safe and explicit:
             throw new AccessDeniedException("Authenticated user's CompanyID could not be determined. Update not allowed.");
        }

        ContactDto contactDto = request.getContact();
        ContactDto updatedContactDto = contactService.updateContact(contactDto, requestingUserCompanyId);
        log.info("Successfully updated contact with ID: {}", updatedContactDto.getId());
        return ResponseEntity.ok(updatedContactDto);
    }

    @DeleteMapping("/DeleteContact")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<DeleteContactResponseDto> deleteContact(@RequestParam Integer contactId, @AuthenticationPrincipal User user) {
        log.info("Received request to delete contact with ID: {}", contactId);
        Integer requestingUserCompanyId = null;

        if (user != null && user.getCompanyID() != null) {
            log.info("Request made by user: {}, CompanyID: {}", user.getUsername(), user.getCompanyID());
            requestingUserCompanyId = user.getCompanyID();
        } else {
            log.warn("User details or CompanyID not available for delete request. User: {}", (user != null ? user.getUsername() : "null"));
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new DeleteContactResponseDto("User authentication error or CompanyID missing.", false));
        }

        if (contactId == null) {
             log.warn("Delete request failed: contactId parameter is missing.");
             return ResponseEntity.badRequest().body(new DeleteContactResponseDto("contactId parameter is required.", false));
        }

        DeleteContactResponseDto response = contactService.deleteContact(contactId, requestingUserCompanyId);
        
        if (response.isSuccess()) {
            log.info("Contact deletion successful for ID: {}. Message: {}", contactId, response.getMessage());
            return ResponseEntity.ok(response);
        } else {
            log.warn("Contact deletion failed for ID: {}. Message: {}", contactId, response.getMessage());
            // Consider different HTTP status codes based on the failure reason if more granularity is needed from controller
            // For now, service layer DTO carries the main status, and we return 200 OK with success:false
            // or a more specific error if it was a client/auth error handled in controller.
            return ResponseEntity.ok(response); // Or .status(HttpStatus.BAD_REQUEST) or .status(HttpStatus.NOT_FOUND) etc.
        }
    }

    @GetMapping("/GetContact")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<?> getContactById(@RequestParam Integer contactId, @AuthenticationPrincipal User user) {
        log.info("Received request to get contact with ID: {}", contactId);
        Integer requestingUserCompanyId;

        if (user != null && user.getCompanyID() != null) {
            log.info("Request made by user: {}, CompanyID: {}", user.getUsername(), user.getCompanyID());
            requestingUserCompanyId = user.getCompanyID();
        } else {
            log.warn("User details or CompanyID not available for get contact request. User: {}", (user != null ? user.getUsername() : "null"));
            // Return 401/403 if user/companyId is missing, to be caught by GlobalExceptionHandler or Spring Security
            // For an explicit response here:
            // return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("error", "User authentication error or CompanyID missing."));
            // Throwing an exception is often cleaner to let centralized handlers manage it.
            throw new AccessDeniedException("User authentication error or CompanyID missing.");
        }

        if (contactId == null) {
            log.warn("Get contact request failed: contactId parameter is missing.");
            // return ResponseEntity.badRequest().body(Map.of("error", "contactId parameter is required."));
            // Throwing for consistency with service layer and centralized handling:
            throw new IllegalArgumentException("contactId parameter is required."); 
        }
        
        // Service method will throw EntityNotFoundException or AccessDeniedException if needed.
        // These should be handled by a @ControllerAdvice (GlobalExceptionHandler)
        ContactDto contactDto = contactService.getContactByIdAndCompanyId(contactId, requestingUserCompanyId);
        
        Map<String, ContactDto> response = new HashMap<>();
        response.put("contact", contactDto);
        
        log.info("Successfully retrieved contact with ID: {}", contactId);
        return ResponseEntity.ok(response);
    }
} 