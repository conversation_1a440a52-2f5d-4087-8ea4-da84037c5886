package org.example.nbk.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.nbk.entity.User;
import org.example.nbk.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class HelloController {

    private final UserService userService; // Made final and constructor injected

    @Autowired
    public HelloController(UserService userService) { // Constructor injection
        this.userService = userService;
    }

    @GetMapping({"/", "/health"})
    public ResponseEntity<String> heartbeat() {
        return ResponseEntity.ok("OK");
    }

    @GetMapping("/hello")
    public String hello() {
        log.info("GET /api/hello called");
        return "Hello from NBK API!";
    }

    @GetMapping("/dbtest/users")
    public ResponseEntity<?> getTop5Users() {
        log.info("Received request for /api/dbtest/users");
        // userService will be non-null due to constructor injection or app context will fail to start
        try {
            List<User> users = userService.getTop5Users();
            if (users.isEmpty()) {
                log.info("No users found by UserService, returning HTTP 204 No Content.");
                return ResponseEntity.noContent().build();
            }
            log.info("Successfully retrieved {} users, returning them.", users.size());
            log.info("Users: {}", users);
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            log.error("API error fetching top 5 users: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error fetching users: " + e.getMessage());
        }
    }
} 