package org.example.nbk.service;

import org.example.nbk.entity.RequestResponse;
import jakarta.servlet.http.HttpServletRequest;

public interface AuthorizeService {
    /**
     * Validates the authentication token in the request
     * This is equivalent to the C# Authorize.RequestTokenAuth method
     * 
     * @param request The HTTP request containing the authorization header
     * @return RequestResponse with success/failure status and message
     */
    RequestResponse requestTokenAuth(HttpServletRequest request);
} 