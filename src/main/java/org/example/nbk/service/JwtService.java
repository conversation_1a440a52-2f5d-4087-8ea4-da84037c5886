package org.example.nbk.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;
import java.nio.charset.StandardCharsets;

@Service
public class JwtService {

    private final SecretKey secretKey;
    private final long expirationMs;

    public JwtService(
            @Value("${nbk.jwt.secret}") String secret,
            @Value("${nbk.jwt.expiration-ms}") long expirationMs) {
        // Ensure the secret key is long enough for HS256
        if (secret == null || secret.length() < 32) {
             // Use a default secure key ONLY FOR DEV if not configured properly
            // WARNING: Do NOT use this default in production!
            // Generate one using: echo $(openssl rand -base64 32) 
            secret = "DefaultDevelopmentSecretKeyMustBeReplacedInProdEnv";
            // Consider logging a warning here
            System.err.println("WARNING: nbk.jwt.secret is not configured or too short. Using insecure default key for development.");
        }
        this.secretKey = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        this.expirationMs = expirationMs;
    }

    public String generateToken(Integer userId) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expirationMs);

        return Jwts.builder()
                // Use standard subject claim for user ID
                .setSubject(String.valueOf(userId))
                // Add custom claim if needed, like original 'unique_name'
                // .claim("unique_name", String.valueOf(userId))
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(secretKey, SignatureAlgorithm.HS256)
                .compact();
    }

    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                .setSigningKey(secretKey)
                .build()
                .parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public Integer getUserIdFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
            .setSigningKey(secretKey)
            .build()
            .parseClaimsJws(token)
            .getBody();
        
        return Integer.parseInt(claims.getSubject());
    }
} 