package org.example.nbk.service;

import lombok.extern.slf4j.Slf4j;
import org.example.nbk.dto.LoginRequestDto;
import org.example.nbk.dto.LoginResponseDto;
import org.example.nbk.entity.User;
import org.example.nbk.exception.AuthenticationException;
import org.example.nbk.repository.UserRepository;
import org.example.nbk.util.DesEncryptionUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional; // Needed for DB update
import jakarta.persistence.EntityNotFoundException; // For throwing if user not found

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections; // For Collections.emptyList()
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserService {

    private final UserRepository userRepository;
    private final JwtService jwtService;
    // DesEncryptionUtil is static, no need to inject
    
    // 存储已登录用户的内存列表，与C#版本一致
    private final List<User> loggedInUsers = Collections.synchronizedList(new ArrayList<>());

    @Autowired
    public UserService(UserRepository userRepository, JwtService jwtService) {
        this.userRepository = userRepository;
        this.jwtService = jwtService;
    }

    /**
     * 获取所有已登录过的用户
     * 与C#版本保持一致，只返回内存列表中的用户
     * @return 所有已登录用户的列表
     */
    public List<User> getAllUsers() {
        log.info("Returning list of logged in users, total: {}", loggedInUsers.size());
        // 返回已登录用户的复制列表，隐藏密码
        return loggedInUsers.stream()
                .map(user -> {
                    User userWithoutPassword = new User();
                    // 手动复制所有需要的字段，避免命名不一致问题
                    userWithoutPassword.setId(user.getId());
                    userWithoutPassword.setFullName(user.getFullName());
                    userWithoutPassword.setUserName(user.getUsername()); // 注意这里使用了getUsername()
                    userWithoutPassword.setDesignation(user.getDesignation());
                    userWithoutPassword.setEmail(user.getEmail());
                    userWithoutPassword.setUserTypeID(user.getUserTypeID());
                    userWithoutPassword.setAddress(user.getAddress());
                    userWithoutPassword.setContactNo(user.getContactNo());
                    userWithoutPassword.setIsActive(user.getIsActive());
                    userWithoutPassword.setPicture(user.getPicture());
                    userWithoutPassword.setContactId(user.getContactId());
                    userWithoutPassword.setIsAdmin(user.getIsAdmin());
                    userWithoutPassword.setCompanyID(user.getCompanyID());
                    userWithoutPassword.setIsSystemOwner(user.getIsSystemOwner());
                    // 明确设置密码为null
                    userWithoutPassword.setPassword(null);
                    return userWithoutPassword;
                })
                .collect(Collectors.toList());
    }

    public List<User> getTop5Users() {
        log.info("Attempting to fetch top 5 users from schema 'nbkUser', table 'Users'.");
        try {
            List<User> users = userRepository.findTop5ByOrderByIdAsc();
            if (users.isEmpty()) {
                log.warn("No users found in nbkUser.Users table, or the table is empty.");
            } else {
                log.info("Successfully fetched {} users.", users.size());
            }
            return users;
        } catch (Exception e) {
            // Log the full stack trace for detailed debugging
            log.error("Error fetching users from database. Schema: nbkUser, Table: Users. Error: {}", e.getMessage(), e);
            // Depending on policy, either rethrow or return empty/null
            // For now, returning an empty list to prevent API from breaking, but this hides DB errors from client directly.
            return Collections.emptyList(); 
        }
    }

    @Transactional // Ensure token update and save happen atomically
    public LoginResponseDto authenticate(LoginRequestDto request) {
        log.info("Authenticating user: {}", request.getUserName());

        String encryptedPassword = DesEncryptionUtil.encrypt(request.getPassword());
        log.debug("Password encrypted for user: {}", request.getUserName());

        Optional<User> userOptional = userRepository.findByUserName(request.getUserName());
        if (userOptional.isEmpty()) {
            log.warn("Authentication failed: User not found - {}", request.getUserName());
            throw new AuthenticationException("User not authenticated, Please check user name or password!");
        }

        User user = userOptional.get();
        log.debug("User found: {}. Comparing passwords.", request.getUserName());

        // Compare encrypted passwords
        if (!encryptedPassword.equals(user.getPassword())) {
            log.warn("Authentication failed: Password mismatch for user - {}", request.getUserName());
            throw new AuthenticationException("User not authenticated, Please check user name or password!");
        }
        log.debug("Password matched for user: {}. Performing UserType/IsMobileApp check.", request.getUserName());

        // Perform UserType validation based on isMobileApp flag
        Integer userTypeId = user.getUserTypeID();
        Boolean isMobileApp = request.getIsMobileApp();

        if (Boolean.FALSE.equals(isMobileApp) && userTypeId != null && userTypeId == 2) {
            log.warn("Authentication failed: User (ID:{}) is UserType 2, not allowed for non-mobile app login.", user.getId());
            throw new AuthenticationException("User not authenticated, Please check user name or password!");
        }
        if (Boolean.TRUE.equals(isMobileApp) && userTypeId != null && userTypeId == 1) {
            log.warn("Authentication failed: User (ID:{}) is UserType 1, not allowed for mobile app login.", user.getId());
            throw new AuthenticationException("User not authenticated, Please check user name or password!");
        }
        log.info("User type validation passed for user: {}. Generating token.", request.getUserName());

        // Generate JWT
        String token = jwtService.generateToken(user.getId());

        // Update user entity with token details
        LocalDateTime now = LocalDateTime.now();
        // Assuming 7 day expiration is handled by JWT itself, but DB fields might need update
        user.setToken(token);
        user.setTokenValidFrom(now);
        // Calculate expiry based on JWT service's knowledge, or roughly add 7 days
        user.setTokenValidTo(now.plusDays(7)); // Align with JWT expiration if possible

        userRepository.save(user);
        
        // 添加到已登录用户列表中，与C#版本保持一致
        if (!loggedInUsers.contains(user)) {
            loggedInUsers.add(user);
            log.debug("Added user {} to logged in users list, total count: {}", user.getUsername(), loggedInUsers.size());
        }
        
        log.info("User {} authenticated successfully. Token generated and user updated.", user.getUsername());

        // Prepare and return response DTO
        return new LoginResponseDto(
                user.getId(),
                user.getFullName(),
                user.getUsername(),
                token,
                user.getIsAdmin(),
                isMobileApp, // Reflecting the input flag
                user.getCompanyID()
        );
    }

    public User findUserById(Integer userId) {
        log.debug("Attempting to find user by ID: {}", userId);
        return userRepository.findById(userId)
                .orElseThrow(() -> {
                    log.warn("User not found with ID: {}", userId);
                    return new EntityNotFoundException("User not found with ID: " + userId);
                });
    }
} 