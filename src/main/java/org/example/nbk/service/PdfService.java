package org.example.nbk.service;

import org.example.nbk.dto.workflow.ProjectWorkflowDto;
import org.example.nbk.entity.PostNumber;
import org.example.nbk.entity.Project;
import org.example.nbk.entity.Service;
import org.example.nbk.entity.GeneralSetting;

import java.util.List;

public interface PdfService {

    class PdfTemplateData {
        public Project project;
        public List<Service> services;
        public PostNumber postNumber;
        public GeneralSetting settings;

        public PdfTemplateData(Project project, List<Service> services, PostNumber postNumber, GeneralSetting settings) {
            this.project = project;
            this.services = services;
            this.postNumber = postNumber;
            this.settings = settings;
        }
    }
    String generatePdfForStepTwo(ProjectWorkflowDto param, PdfTemplateData data) throws Exception;
    
    String generatePdfForStepThirteen(ProjectWorkflowDto param, PdfTemplateData data) throws Exception;

    String generatePdfForStepFourteen(ProjectWorkflowDto param, PdfTemplateData data) throws Exception;
} 