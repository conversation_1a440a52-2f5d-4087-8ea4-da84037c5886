package org.example.nbk.service;

import org.example.nbk.dto.ContactDto;
import org.example.nbk.dto.DeleteContactResponseDto;
import java.util.List;

public interface ContactService {
    /**
     * Creates a new contact based on the provided DTO.
     * @param contactDto Data transfer object containing contact details.
     * @param companyId The ID of the company for the user creating the contact.
     * @return Data transfer object of the created contact, including its new ID.
     */
    ContactDto createContact(ContactDto contactDto, Integer companyId);

    /**
     * Retrieves all contacts associated with a given company ID.
     * @param companyId The ID of the company whose contacts are to be retrieved.
     * @return A list of data transfer objects for the contacts found.
     */
    List<ContactDto> getAllContactsByCompanyId(Integer companyId);

    /**
     * Updates an existing contact.
     * @param contactDto Data transfer object containing contact details to update. ID must be present.
     * @param requestingUserCompanyId The CompanyID of the user making the update request, for authorization.
     * @return Data transfer object of the updated contact.
     * @throws jakarta.persistence.EntityNotFoundException if the contact with the given ID is not found.
     * @throws org.springframework.security.access.AccessDeniedException if the user is not authorized to update the contact.
     */
    ContactDto updateContact(ContactDto contactDto, Integer requestingUserCompanyId);

    /**
     * Deletes a contact after checking for dependencies and authorization.
     * @param contactId The ID of the contact to delete.
     * @param requestingUserCompanyId The CompanyID of the user making the delete request, for authorization.
     * @return A DTO containing a message and a success flag.
     */
    DeleteContactResponseDto deleteContact(Integer contactId, Integer requestingUserCompanyId);

    /**
     * Retrieves a specific contact by its ID, ensuring it belongs to the requesting user's company.
     * @param contactId The ID of the contact to retrieve.
     * @param requestingUserCompanyId The CompanyID of the user making the request, for authorization.
     * @return Data transfer object of the found contact.
     * @throws jakarta.persistence.EntityNotFoundException if the contact with the given ID is not found.
     * @throws org.springframework.security.access.AccessDeniedException if the user is not authorized to access the contact.
     */
    ContactDto getContactByIdAndCompanyId(Integer contactId, Integer requestingUserCompanyId);
} 