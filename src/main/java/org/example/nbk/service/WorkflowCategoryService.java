package org.example.nbk.service;

import org.example.nbk.dto.RequestResponse;
import org.example.nbk.dto.WorkflowCategoryDto;
import org.example.nbk.dto.WorkflowCategoryStepDto;
import org.example.nbk.dto.WrapperMultiWorkflowCategory;
import org.example.nbk.dto.WrapperMultiWorkflowCategorySteps;
import org.example.nbk.dto.WrapperWorkflowCategory;
import org.example.nbk.dto.WrapperWorkflowCategoryStep;

public interface WorkflowCategoryService {
    
    // 工作流类别相关方法
    WrapperWorkflowCategory getSingleWorkflowCategory(Integer workflowCategoryId);
    
    WrapperWorkflowCategory updateSingleWorkflowCategory(WorkflowCategoryDto workflowCategoryDto);
    
    RequestResponse deleteSingleWorkflowCategory(Integer workflowCategoryId);
    
    WrapperWorkflowCategory createSingleWorkflowCategory(WorkflowCategoryDto workflowCategoryDto);
    
    WrapperMultiWorkflowCategory getAllWorkflowCategory();
    
    // 工作流类别步骤相关方法
    WrapperMultiWorkflowCategorySteps getSingleWorkflowCategoryStepsForOneWorkflow(Integer workflowCategoryId);
    
    WrapperWorkflowCategoryStep createSingleWorkflowCategoryStep(WorkflowCategoryStepDto workflowCategoryStepDto);
    
    WrapperWorkflowCategoryStep getSingleWorkflowCategoryStep(Integer workflowCategoryStepId);
    
    WrapperWorkflowCategoryStep updateSingleWorkflowCategoryStep(WorkflowCategoryStepDto workflowCategoryStepDto);
    
    RequestResponse deleteSingleWorkflowCategoryStep(Integer workflowCategoryStepId);
} 