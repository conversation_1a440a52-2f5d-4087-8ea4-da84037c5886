package org.example.nbk.service.impl;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.nbk.config.StaticDetails;
import org.example.nbk.service.IS3Service;
import org.example.nbk.service.S3Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Calendar;

@Service
@RequiredArgsConstructor
@Slf4j
public class S3ServiceImpl implements S3Service, IS3Service {

    private final AmazonS3 amazonS3;

    @Value("${aws.s3.bucket.name:nksystem-storage}")
    private String bucketName;

    @Value("${aws.s3.url.static.part:.s3.amazonaws.com/}")
    private String urlStaticPart;

    @Override
    public CompletableFuture<String> uploadFileAsync(String bucketFolder, MultipartFile file, String fileName) {
        log.debug("异步上传文件到S3，桶文件夹: {}, 文件名: {}", bucketFolder, fileName);
        
        // 创建ObjectMetadata并设置内容长度，避免警告
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.getSize());
        metadata.setContentType(file.getContentType());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 调用同步上传方法，传入预先创建的metadata
                InputStream inputStream = file.getInputStream();
                String result = uploadFileFromStream(bucketFolder, inputStream, fileName, metadata);
                log.debug("文件上传成功: {}", result);
                return result;
            } catch (Exception e) {
                log.error("文件上传失败: {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }, Executors.newCachedThreadPool());
    }

    @Override
    public String createPublicURL(String bucketName, String urlStaticPart, String bucketFolder, String fileName) {
        log.debug("创建公共URL，桶名称: {}, URL静态部分: {}, 桶文件夹: {}, 文件名: {}",
                bucketName, urlStaticPart, bucketFolder, fileName);
        
        return createPublicUrl(bucketName, urlStaticPart, bucketFolder, fileName);
    }

    @Override
    public String uploadFile(String bucketFolder, MultipartFile file, String fileName) {
        try {
            // 创建ObjectMetadata并设置内容长度
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());
            
            // 使用文件流和元数据上传
            return uploadFileFromStream(bucketFolder, file.getInputStream(), fileName, metadata);
        } catch (IOException e) {
            throw new RuntimeException("Failed to upload file to S3", e);
        }
    }

    @Override
    public String uploadFileFromStream(String bucketFolder, InputStream stream, String fileName) {
        // 由于无法获取流的大小，调用不设置内容长度的方法
        ObjectMetadata metadata = new ObjectMetadata();
        return uploadFileFromStream(bucketFolder, stream, fileName, metadata);
    }
    
    /**
     * 使用指定的元数据上传文件到S3
     * 
     * @param bucketFolder S3存储桶中的文件夹路径
     * @param stream 文件输入流
     * @param fileName 文件名
     * @param metadata 文件元数据，包含内容长度等信息
     * @return 上传结果，成功返回"Success"
     */
    private String uploadFileFromStream(String bucketFolder, InputStream stream, String fileName, ObjectMetadata metadata) {
        try {
            // 确保文件夹路径正确格式化（去除多余斜杠）
            String normalizedBucketFolder = normalizePath(bucketFolder);
            String normalizedFileName = fileName.startsWith("/") ? fileName.substring(1) : fileName;
            
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    bucketName, // 使用固定桶名
                    normalizedBucketFolder + normalizedFileName, // 文件路径组合为key
                    stream, 
                    metadata
            );
            amazonS3.putObject(putObjectRequest);
            return "Success";
        } catch (AmazonServiceException e) {
            throw new RuntimeException("Error uploading file to S3: " + e.getMessage(), e);
        }
    }

    @Override
    public String createPublicUrl(String bucketName, String urlStaticPart, String bucketFolder, String fileName) {
        // 规范化路径，避免重复的"/"
        String normalizedBucketFolder = normalizePath(bucketFolder);
        String normalizedFileName = fileName.startsWith("/") ? fileName.substring(1) : fileName;
        
        // 使用固定的桶名，忽略传入的参数
        String url = "https://" + this.bucketName + this.urlStaticPart + normalizedBucketFolder + normalizedFileName;
        return url;
    }

    @Override
    public InputStream getFileFromS3(String bucketFolder, String fileName) {
        try {
            // 规范化路径
            String normalizedBucketFolder = normalizePath(bucketFolder);
            String normalizedFileName = fileName.startsWith("/") ? fileName.substring(1) : fileName;
            String key = normalizedBucketFolder + normalizedFileName;
            
            S3Object object = amazonS3.getObject(bucketName, key);
            return object.getObjectContent();
        } catch (AmazonServiceException e) {
            return null;
        }
    }

    @Override
    public boolean createFolder(String folderPath) {
        try {
            // 规范化路径，确保以"/"结尾
            String normalizedPath = normalizePath(folderPath);
            if (!normalizedPath.endsWith("/")) {
                normalizedPath = normalizedPath + "/";
            }
            
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(0);
            
            amazonS3.putObject(
                new PutObjectRequest(
                    bucketName,
                    normalizedPath,
                    new ByteArrayInputStream(new byte[0]), 
                    metadata
                )
            );
            return true;
        } catch (AmazonServiceException e) {
            return false;
        }
    }

    @Override
    public boolean folderExists(String folderPath) {
        try {
            // 规范化路径，确保以"/"结尾
            String normalizedPath = normalizePath(folderPath);
            if (!normalizedPath.endsWith("/")) {
                normalizedPath = normalizedPath + "/";
            }
            
            return amazonS3.doesObjectExist(bucketName, normalizedPath);
        } catch (AmazonServiceException e) {
            return false;
        }
    }

    @Override
    public String getCompanyBucketName(Integer companyId) {
        // 返回固定桶名，忽略companyId
        return bucketName;
    }

    @Override
    public String getCompanyS3Folder(Integer companyId) {
        return "company/" + (companyId == null ? 1 : companyId) + "/";
    }

    @Override
    public String getCompanyS3UrlStaticPart(Integer companyId) {
        return urlStaticPart;
    }

    @Override
    public Map<String, String> getAllS3Paths(Integer companyId) {
        Integer effectiveCompanyId = companyId == null ? 1 : companyId;
        
        Map<String, String> paths = new HashMap<>();
        paths.put("bucketName", bucketName);
        paths.put("companyFolder", getCompanyS3Folder(effectiveCompanyId));
        paths.put("urlStaticPart", urlStaticPart);
        paths.put("pdfFolder", StaticDetails.S3_BUCKET_FOLDER_FOR_PDF);
        paths.put("checklistTypeImagesFolder", StaticDetails.S3_BUCKET_FOLDER_FOR_INSPECTION_CHECKLIST_IMAGES);
        paths.put("devChecklistPdfsFolder", StaticDetails.S3_BUCKET_FOLDER_FOR_INSPECTION_THIRD_PARTY_PDF);
        paths.put("icsFolder", StaticDetails.S3_BUCKET_FOLDER_FOR_CALANDER_ICS);
        paths.put("pdfHtmlFolder", StaticDetails.S3_BUCKET_FOLDER_FOR_PDF_HTML_FILES);
        paths.put("projectSiteImagesFolder", StaticDetails.S3_BUCKET_FOLDER_FOR_PROJECT_SITE_IMAGES);
        paths.put("resourcesFolder", "Resources/");
        
        return paths;
    }

    @Override
    public boolean initializeCompanyFolders(Integer companyId, String resourceFile) {
        Integer effectiveCompanyId = companyId == null ? 1 : companyId;
        String companyBaseFolder = getCompanyS3Folder(effectiveCompanyId);
        
        // 创建匹配C#版本StaticDetails中定义的所有文件夹
        boolean success = true;
        
        // 1. 创建公司基础文件夹
        success &= createFolder(companyBaseFolder);
        
        // 2. 创建各种子文件夹，对应C#版本的各种路径
        success &= createFolder(companyBaseFolder + StaticDetails.S3_BUCKET_FOLDER_FOR_PDF);
        success &= createFolder(companyBaseFolder + StaticDetails.S3_BUCKET_FOLDER_FOR_INSPECTION_CHECKLIST_IMAGES);
        success &= createFolder(companyBaseFolder + StaticDetails.S3_BUCKET_FOLDER_FOR_INSPECTION_THIRD_PARTY_PDF);
        success &= createFolder(companyBaseFolder + StaticDetails.S3_BUCKET_FOLDER_FOR_CALANDER_ICS);
        success &= createFolder(companyBaseFolder + StaticDetails.S3_BUCKET_FOLDER_FOR_PDF_HTML_FILES);
        success &= createFolder(companyBaseFolder + StaticDetails.S3_BUCKET_FOLDER_FOR_PROJECT_SITE_IMAGES);
        success &= createFolder(companyBaseFolder + "Resources/");
        
        // 如果提供了resourceFile，则创建该文件
        if (resourceFile != null && !resourceFile.isEmpty()) {
            try {
                // 创建一个空的占位文件
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(0);
                
                String normalizedResourcePath = normalizePath(
                        companyBaseFolder + "Resources/" + resourceFile);
                
                amazonS3.putObject(
                    bucketName,
                    normalizedResourcePath,
                    new ByteArrayInputStream(new byte[0]), 
                    metadata
                );
            } catch (Exception e) {
                success = false;
            }
        }
        
        return success;
    }
    
    /**
     * 规范化路径，避免重复的"/"
     * 
     * @param path 原始路径
     * @return 规范化后的路径
     */
    private String normalizePath(String path) {
        if (path == null || path.isEmpty()) {
            return "";
        }
        
        // 去除开头的"/"
        String normalizedPath = path.startsWith("/") ? path.substring(1) : path;
        
        // 去除多余的"/"
        normalizedPath = normalizedPath.replaceAll("/+", "/");
        
        return normalizedPath;
    }

    @Override
    public boolean deleteFile(String bucketFolder, String fileName) {
        try {
            log.debug("正在删除S3文件，桶文件夹: {}, 文件名: {}", bucketFolder, fileName);
            
            // 规范化路径
            String normalizedBucketFolder = normalizePath(bucketFolder);
            String normalizedFileName = fileName.startsWith("/") ? fileName.substring(1) : fileName;
            String key = normalizedBucketFolder + normalizedFileName;
            
            // 检查文件是否存在 - 为了兼容旧版本，不存在也返回true
            if (!amazonS3.doesObjectExist(bucketName, key)) {
                log.debug("S3文件不存在，跳过删除: {}", key);
                return true; // 返回true，表示操作"成功"
            }
            
            // 删除文件
            amazonS3.deleteObject(bucketName, key);
            log.debug("S3文件已成功删除: {}", key);
            return true;
        } catch (AmazonServiceException e) {
            log.debug("删除S3文件时发生错误(不影响业务): {}", e.getMessage());
            return true; // 即使出现异常也返回true，确保上层调用不受影响
        }
    }

    @Override
    public boolean deleteFile(String fileUrl) {
        try {
            log.debug("通过URL删除S3文件: {}", fileUrl);
            
            // 尝试从URL中提取桶文件夹路径和文件名
            if (fileUrl == null || fileUrl.isEmpty()) {
                return false;
            }
            
            // 预期URL格式为：https://{bucketName}{urlStaticPart}{bucketFolder}{fileName}
            String urlPrefix = "https://" + bucketName + urlStaticPart;
            if (!fileUrl.startsWith(urlPrefix)) {
                log.warn("无效的S3文件URL格式: {}", fileUrl);
                return false;
            }
            
            // 提取相对路径（去除URL前缀）
            String relativePath = fileUrl.substring(urlPrefix.length());
            
            // 查找最后一个斜杠来分离文件夹和文件名
            int lastSlashIndex = relativePath.lastIndexOf('/');
            if (lastSlashIndex < 0) {
                // 没有文件夹部分，直接删除文件
                return deleteFile("", relativePath);
            }
            
            String bucketFolder = relativePath.substring(0, lastSlashIndex + 1); // 包含末尾斜杠
            String fileName = relativePath.substring(lastSlashIndex + 1);
            
            return deleteFile(bucketFolder, fileName);
        } catch (Exception e) {
            log.error("删除S3文件时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String generatePresignedUrl(String fileUrl, int expirationTimeInMinutes) {
        try {
            log.debug("生成S3文件预签名URL，文件URL: {}，过期时间: {} 分钟", fileUrl, expirationTimeInMinutes);
            
            // 检查参数有效性
            if (fileUrl == null || fileUrl.isEmpty() || expirationTimeInMinutes <= 0) {
                log.warn("生成预签名URL的参数无效，fileUrl: {}, expirationTimeInMinutes: {}", 
                        fileUrl, expirationTimeInMinutes);
                return null;
            }
            
            // 从URL中提取S3对象键
            String urlPrefix = "https://" + bucketName + urlStaticPart;
            if (!fileUrl.startsWith(urlPrefix)) {
                log.warn("无效的S3文件URL格式: {}", fileUrl);
                return null;
            }
            
            // 提取S3对象键（相对路径）
            String s3Key = fileUrl.substring(urlPrefix.length());
            
            // 验证对象是否存在
            if (!amazonS3.doesObjectExist(bucketName, s3Key)) {
                log.warn("S3文件不存在: {}", s3Key);
                return null;
            }
            
            // 设置过期时间
            Date expiration = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(expiration);
            cal.add(Calendar.MINUTE, expirationTimeInMinutes);
            expiration = cal.getTime();
            
            // 生成预签名URL
            GeneratePresignedUrlRequest generatePresignedUrlRequest = 
                    new GeneratePresignedUrlRequest(bucketName, s3Key)
                    .withMethod(HttpMethod.GET)
                    .withExpiration(expiration);
            
            URL presignedUrl = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
            
            if (presignedUrl != null) {
                log.debug("成功生成预签名URL: {}", presignedUrl.toString());
                return presignedUrl.toString();
            } else {
                log.warn("生成预签名URL失败");
                return null;
            }
        } catch (AmazonServiceException e) {
            log.error("生成预签名URL时发生Amazon服务异常: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("生成预签名URL时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String uploadFile(MultipartFile file, String path) {
        log.info("上传文件到S3，路径: {}", path);
        try {
            // 解析路径，分离出文件夹和文件名
            int lastSlashIndex = path.lastIndexOf('/');
            String bucketFolder = "";
            String fileName = path;
            
            if (lastSlashIndex > 0) {
                bucketFolder = path.substring(0, lastSlashIndex + 1);
                fileName = path.substring(lastSlashIndex + 1);
            }
            
            // 调用具体的上传实现
            return uploadFile(bucketFolder, file, fileName);
        } catch (Exception e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to upload file: " + e.getMessage(), e);
        }
    }

    @Override
    public String getBucketNameByCompanyId(Integer companyId) {
        // 与getCompanyBucketName实现相同，返回固定的桶名
        return bucketName;
    }

    @Override
    public String getFileContentAsString(String bucketFolder, String fileName) {
        InputStream inputStream = getFileFromS3(bucketFolder, fileName);
        if (inputStream == null) {
            return null;
        }
        try (java.util.Scanner scanner = new java.util.Scanner(inputStream, java.nio.charset.StandardCharsets.UTF_8.name())) {
            return scanner.useDelimiter("\\A").next();
        } catch (java.util.NoSuchElementException e) {
            return "";
        }
    }

    @Override
    public void uploadFile(String bucketFolder, InputStream stream, String fileName, String contentType) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(contentType);
        uploadFileFromStream(bucketFolder, stream, fileName, metadata);
    }

    @Override
    public String getFileAsBase64(String bucketFolder, String fileName) {
        try (InputStream inputStream = getFileFromS3(bucketFolder, fileName)) {
            if (inputStream == null) {
                log.warn("File not found in S3: bucket={}, key={}", bucketFolder, fileName);
                return "";
            }
            byte[] fileBytes = inputStream.readAllBytes();
            return Base64.getEncoder().encodeToString(fileBytes);
        } catch (IOException e) {
            log.error("Failed to read file from S3 and encode to Base64: bucket={}, key={}", bucketFolder, fileName, e);
            return "";
        }
    }
} 