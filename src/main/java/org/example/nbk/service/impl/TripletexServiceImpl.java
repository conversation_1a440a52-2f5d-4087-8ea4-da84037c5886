package org.example.nbk.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.example.nbk.dto.RequestResponse;
import org.example.nbk.dto.third.tripletex.*;
import org.example.nbk.dto.third.tripletex.auth.SessionTokenResponseDto;
import org.example.nbk.dto.workflow.ProjectInvoiceDataDto;
import org.example.nbk.dto.workflow.WrapperProjectInvoiceDataDto;
import org.example.nbk.entity.ContactBook;
import org.example.nbk.entity.Project;
import org.example.nbk.entity.Service;
import org.example.nbk.repository.ContactRepository;
import org.example.nbk.repository.ProjectRepository;
import org.example.nbk.repository.ServiceRepository;
import org.example.nbk.service.TripletexService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TripletexServiceImpl implements TripletexService {

    private final RestTemplate restTemplate;
    private final ProjectRepository projectRepository;
    private final ContactRepository contactRepository;
    private final ServiceRepository serviceRepository;
    private final String baseUrl;
    private final String consumerToken;
    private final String employeeToken;
    private final String companyId;

    private String sessionToken;

    public TripletexServiceImpl(RestTemplate restTemplate,
                                ProjectRepository projectRepository,
                                ContactRepository contactRepository,
                                ServiceRepository serviceRepository,
                                @Value("${tripletex.api.base-url}") String baseUrl,
                                @Value("${tripletex.api.consumer-token}") String consumerToken,
                                @Value("${tripletex.api.employee-token}") String employeeToken,
                                @Value("${tripletex.api.company-id}") String companyId) {
        this.restTemplate = restTemplate;
        this.projectRepository = projectRepository;
        this.contactRepository = contactRepository;
        this.serviceRepository = serviceRepository;
        this.baseUrl = baseUrl;
        this.consumerToken = consumerToken;
        this.employeeToken = employeeToken;
        this.companyId = companyId;
    }

    @Override
    @Transactional
    public RequestResponse createTripletexOrderFromProject(Integer projectId) {
        try {
            Project project = projectRepository.findByIdWithDetailsForTripletex(projectId)
                    .orElseThrow(() -> new RuntimeException("Project not found with id: " + projectId));

            // 1. Find or Create Customer
            TripletexCustomerDto customer = findOrCreateCustomer(project.getCustomer());
            if (customer == null || customer.getId() == null) {
                throw new RuntimeException("Failed to find or create customer in Tripletex.");
            }

            // 2. Find or Create Products for all services
            List<TripletexProductDto> products = new ArrayList<>();
            for (var projectService : project.getProjectServices()) {
                TripletexProductDto product = findOrCreateProduct(projectService.getService(), projectService.getPrice());
                products.add(product);
            }

            // 3. Create Order (Shell first, then add lines)
            TripletexOrderDto createdOrder = createOrder(project, customer, products);

            // 4. Update local DB
            project.setInvoiceTripletexID(String.valueOf(createdOrder.getId()));
            projectRepository.save(project);

            log.info("Successfully created Tripletex order {} for project {}", createdOrder.getId(), projectId);
            return new RequestResponse(true, "Tripletex order created successfully.");

        } catch (Exception e) {
            log.error("Error creating Tripletex order for project {}", projectId, e);
            return new RequestResponse(false, "Error creating order in Tripletex: " + e.getMessage());
        }
    }

    private TripletexCustomerDto findOrCreateCustomer(ContactBook contact) {
        if (contact.getTripletexId() != null) {
            // In a real scenario, you might want to fetch the customer to confirm it exists.
            // For now, we trust the local ID.
            log.info("Customer {} already has a Tripletex ID: {}. Skipping creation.", contact.getId(), contact.getTripletexId());
            TripletexCustomerDto existingCustomer = new TripletexCustomerDto();
            existingCustomer.setId(Integer.parseInt(contact.getTripletexId()));
            existingCustomer.setName(contact.getName()); // Essential for order creation
            return existingCustomer;
        }

        log.info("Creating new Tripletex customer for contact ID: {}", contact.getId());
        EmployeeDto accountManager = EmployeeDto.builder().id(1180852).build(); // Hardcoded from C#
        TripletexCustomerDto newCustomer = TripletexCustomerDto.builder()
                .name(contact.getCompanyName() != null && !contact.getCompanyName().isBlank() ? contact.getCompanyName() : contact.getName())
                .email(contact.getEmail())
                .invoiceEmail(contact.getEmail())
                .phoneNumberMobile(contact.getContactNo())
                .accountManager(accountManager)
                .invoiceSendMethod("EMAIL")
                .build();

        TripletexResponseDto<TripletexCustomerDto> response = post("/v2/customer", newCustomer, new ParameterizedTypeReference<>() {});
        TripletexCustomerDto createdCustomer = response.getValue();

        // Save new Tripletex ID to local contact book
        contact.setTripletexId(String.valueOf(createdCustomer.getId()));
        contactRepository.save(contact);
        log.info("Successfully created Tripletex customer with ID: {}", createdCustomer.getId());

        return createdCustomer;
    }

    private TripletexProductDto findOrCreateProduct(Service service, String projectSpecificPrice) {
        if (service.getTripletexId() != null) {
            log.info("Product {} already has a Tripletex ID: {}. Checking for price updates.", service.getId(), service.getTripletexId());
            // C# logic also updates the price if it differs.
            // We will do a GET and PUT if necessary.
            return getAndUpdateProductPrice(service, projectSpecificPrice);
        }

        log.info("Creating new Tripletex product for service ID: {}", service.getId());
        VatTypeDto vatType = VatTypeDto.builder().id(3).build(); // Hardcoded from C#
        ProductUnitDto unit = ProductUnitDto.builder().id(448331).build(); // Hardcoded from C#

        BigDecimal price = new BigDecimal(projectSpecificPrice != null ? projectSpecificPrice : service.getRate());

        TripletexProductDto newProduct = TripletexProductDto.builder()
                .name(service.getName())
                .number(String.valueOf(service.getId())) // Using service ID as product number
                .priceExcludingVatCurrency(price)
                .costExcludingVatCurrency(price)
                .vatType(vatType)
                .productUnit(unit)
                .build();

        TripletexResponseDto<TripletexProductDto> response = post("/v2/product", newProduct, new ParameterizedTypeReference<>() {});
        TripletexProductDto createdProduct = response.getValue();

        service.setTripletexId(String.valueOf(createdProduct.getId()));
        serviceRepository.save(service);
        log.info("Successfully created Tripletex product with ID: {}", createdProduct.getId());

        return createdProduct;
    }

    private TripletexProductDto getAndUpdateProductPrice(Service service, String projectSpecificPrice) {
        String url = "/v2/product/" + service.getTripletexId();
        try {
            ResponseEntity<TripletexResponseDto<TripletexProductDto>> response = restTemplate.exchange(
                    baseUrl + url, HttpMethod.GET, new HttpEntity<>(createAuthHeaders()), new ParameterizedTypeReference<>() {});
            
            TripletexProductDto product = Objects.requireNonNull(response.getBody()).getValue();
            BigDecimal currentPrice = product.getPriceExcludingVatCurrency();
            BigDecimal newPrice = new BigDecimal(projectSpecificPrice);

            if (currentPrice.compareTo(newPrice) != 0) {
                log.info("Price for product {} differs. Updating from {} to {}.", product.getId(), currentPrice, newPrice);
                // The C# code creates a special "ProductUpdate" object. We'll build a minimal DTO for the PUT.
                Map<String, BigDecimal> updatePayload = Map.of(
                    "priceExcludingVatCurrency", newPrice,
                    "costExcludingVatCurrency", newPrice
                );
                put(url, updatePayload);
                product.setPriceExcludingVatCurrency(newPrice);
            }
            return product;
        } catch (Exception e) {
            log.error("Failed to get or update product {}. Returning a placeholder.", service.getTripletexId(), e);
            // Fallback to just returning what we know
            TripletexProductDto fallback = new TripletexProductDto();
            fallback.setId(Integer.parseInt(service.getTripletexId()));
            return fallback;
        }
    }


    private TripletexOrderDto createOrder(Project project, TripletexCustomerDto customer, List<TripletexProductDto> products) {
        log.info("Creating order shell for project ID: {}", project.getId());

        String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        
        // Step 1: Create the Order shell without order lines
        TripletexOrderDto orderShell = TripletexOrderDto.builder()
                .customer(customer)
                .orderDate(today)
                .deliveryDate(today)
                .ourContactEmployee(EmployeeDto.builder().id(1180852).build())
                .currency(CurrencyDto.builder().id(1).build())
                .invoicesDueIn(10)
                .invoicesDueInType("DAYS")
                .orderLineSorting("PRODUCT")
                .reference(project.getTitle())
                .orderLines(null) // Explicitly set to null as per C# logic
                .build();

        TripletexResponseDto<TripletexOrderDto> orderShellResponse = post("/v2/order", orderShell, new ParameterizedTypeReference<>() {});
        TripletexOrderDto createdOrderShell = orderShellResponse.getValue();
        log.info("Successfully created order shell with ID: {}", createdOrderShell.getId());

        // Step 2: Add order lines to the newly created order
        addOrderLinesToOrder(createdOrderShell.getId(), project, customer, products);
        
        return createdOrderShell;
    }

    private void addOrderLinesToOrder(Integer orderId, Project project, TripletexCustomerDto customer, List<TripletexProductDto> products) {
        log.info("Adding order lines to order ID: {}", orderId);
        List<TripletexOrderLineDto> orderLines = new ArrayList<>();
        
        List<org.example.nbk.entity.ProjectService> projectServices = project.getProjectServices().stream().toList();

        for (int i = 0; i < projectServices.size(); i++) {
            var projectService = projectServices.get(i);
            var productDto = products.get(i);
            var service = projectService.getService();
            
            BigDecimal price = new BigDecimal(projectService.getPrice() != null ? projectService.getPrice() : service.getRate());
            BigDecimal quantity = new BigDecimal(projectService.getQuantity() != null ? projectService.getQuantity() : 1);
            
            TripletexOrderDto orderForLine = new TripletexOrderDto();
            orderForLine.setId(orderId);
            orderForLine.setCustomer(customer); // C# code includes customer in the order line request
            
            orderLines.add(TripletexOrderLineDto.builder()
                    .product(productDto)
                    .count(quantity)
                    .unitPriceExcludingVatCurrency(price)
                    .unitCostCurrency(price) // Assuming cost is same as price
                    .vatType(VatTypeDto.builder().id(3).build()) // Hardcoded from C#
                    .order(orderForLine) // Nesting the order reference
                    .build());
        }

        // The endpoint in C# is "/order/orderline/list", which suggests a bulk add.
        post("/v2/order/orderline/list", orderLines, new ParameterizedTypeReference<TripletexOrderLineListResponseDto>() {});
        log.info("Successfully added {} order lines to order ID: {}", orderLines.size(), orderId);
    }

    @Override
    public WrapperProjectInvoiceDataDto getInvoiceDetails(Integer projectId) {
        log.info("MOCK: Fetching invoice details for project ID: {}", projectId);
        // Create mock data
        ProjectInvoiceDataDto invoiceData = ProjectInvoiceDataDto.builder()
                .projectId(projectId)
                .invoiceDetails("Mock invoice details for project.")
                .amount(15000.50)
                .build();
        return WrapperProjectInvoiceDataDto.builder()
                .projectInvoiceDataENT(invoiceData)
                .build();
    }

    @Override
    public RequestResponse sendInvoice(Integer projectId) {
        log.info("MOCK: Sending invoice for project ID: {}", projectId);
        // Simulate a successful API call
        RequestResponse response = new RequestResponse();
        response.setSuccess(true);
        response.setMessage("Mock invoice sent successfully to Tripletex.");
        
        return response;
    }
    
    // --- HTTP Helper Methods ---

    private <T, B> T post(String path, B body, ParameterizedTypeReference<T> typeRef) {
        String url = baseUrl + path;
        HttpEntity<B> entity = new HttpEntity<>(body, createAuthHeaders());
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.POST, entity, typeRef);
        return response.getBody();
    }

    private <B> void put(String path, B body) {
        String url = baseUrl + path;
        HttpEntity<B> entity = new HttpEntity<>(body, createAuthHeaders());
        restTemplate.exchange(url, HttpMethod.PUT, entity, Void.class);
    }

    private String getSessionToken() {
        if (this.sessionToken != null) {
            return this.sessionToken;
        }

        log.info("No active session token found. Requesting a new one from Tripletex.");
        String url = baseUrl + "/v2/token/session/:create";
        String expirationDate = LocalDate.now().plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);

        String fullUrl = String.format("%s?consumerToken=%s&employeeToken=%s&expirationDate=%s",
                url, consumerToken, employeeToken, expirationDate);

        HttpHeaders headers = new HttpHeaders();
        // C# code's hardcoded value "MDo1MGQxOWE2Yi05MjE3LTQwOTQtOGUwOC04YTI2YWQ5ZjJiZmY="
        // decodes to "0:50d19a6b-9217-4094-8e08-8a26ad9f2bff"
        String initialAuthCredentials = "0:50d19a6b-9217-4094-8e08-8a26ad9f2bff";
        String encodedAuth = Base64.getEncoder().encodeToString(initialAuthCredentials.getBytes(StandardCharsets.ISO_8859_1));
        headers.set("Authorization", "Basic " + encodedAuth);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<SessionTokenResponseDto> response = restTemplate.exchange(
                    fullUrl, HttpMethod.PUT, entity, SessionTokenResponseDto.class);

            String token = Objects.requireNonNull(response.getBody()).getValue().getToken();
            this.sessionToken = token;
            log.info("Successfully obtained new Tripletex session token.");
            return token;
        } catch (Exception e) {
            log.error("Failed to create Tripletex session token", e);
            throw new RuntimeException("Could not authenticate with Tripletex", e);
        }
    }

    private HttpHeaders createAuthHeaders() {
        String token = getSessionToken();
        String authString = companyId + ":" + token;
        String encodedAuth = Base64.getEncoder().encodeToString(authString.getBytes(StandardCharsets.ISO_8859_1));

        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Basic " + encodedAuth);
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }
}