package org.example.nbk.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.nbk.dto.ContactDto;
import org.example.nbk.dto.DeleteContactResponseDto;
import org.example.nbk.entity.ContactBook;
import org.example.nbk.repository.ContactRepository;
import org.example.nbk.repository.ProjectRepository;
import org.example.nbk.service.ContactService;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ContactServiceImpl implements ContactService {

    private final ContactRepository contactRepository;
    private final ProjectRepository projectRepository;

    @Override
    @Transactional // Ensure atomicity
    public ContactDto createContact(ContactDto contactDto, Integer companyId) {
        log.info("Attempting to create contact for name: {}, CompanyID: {}", contactDto.getName(), companyId);

        // Map DTO to Entity
        ContactBook contactBook = mapDtoToEntity(contactDto);

        // Set CompanyID from the passed parameter
        contactBook.setCompanyId(companyId);

        // Set default values for fields not provided in DTO but present in Entity
        // Adjust these defaults as necessary based on business logic
        if (contactBook.getIsCompany() == null) {
            contactBook.setIsCompany(false); // Default to individual contact
        }
        if (contactBook.getToBeDeleted() == null) {
            contactBook.setToBeDeleted(false); // Default to not deleted
        }
        // Other fields like CityID, Address, etc., will be null if not set here or in mapper

        // Save the new contact entity
        ContactBook savedContact = contactRepository.save(contactBook);
        log.info("Successfully created contact with ID: {}", savedContact.getId());

        // Map saved Entity back to DTO to include the generated ID
        return mapEntityToDto(savedContact);
    }

    @Override
    public List<ContactDto> getAllContactsByCompanyId(Integer companyId) {
        log.info("Attempting to retrieve all contacts for CompanyID: {}", companyId);
        List<ContactBook> contactBooks = contactRepository.findByCompanyId(companyId);
        List<ContactDto> contactDtos = contactBooks.stream()
                .map(this::mapEntityToDto)
                .collect(Collectors.toList());

        // Sort by name (handles null names, case-insensitive, nulls last)
        contactDtos.sort(Comparator.comparing(ContactDto::getName, Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER)));

        log.info("Successfully retrieved and sorted {} contacts for CompanyID: {}", contactDtos.size(), companyId);
        return contactDtos;
    }

    @Override
    @Transactional
    public ContactDto updateContact(ContactDto contactDtoToUpdate, Integer requestingUserCompanyId) {
        log.info("Attempting to update contact with ID: {} by user with CompanyID: {}", contactDtoToUpdate.getId(), requestingUserCompanyId);

        if (contactDtoToUpdate.getId() == null) {
            log.warn("Update failed: Contact ID is null.");
            throw new IllegalArgumentException("Contact ID cannot be null for an update operation.");
        }

        ContactBook existingContact = contactRepository.findById(contactDtoToUpdate.getId())
                .orElseThrow(() -> {
                    log.warn("Update failed: Contact not found with ID: {}", contactDtoToUpdate.getId());
                    return new EntityNotFoundException("Contact not found with id: " + contactDtoToUpdate.getId());
                });

        // Authorization check
        if (!existingContact.getCompanyId().equals(requestingUserCompanyId)) {
            log.warn("Authorization failed: User with CompanyID {} attempted to update contact {} belonging to CompanyID {}.",
                    requestingUserCompanyId, existingContact.getId(), existingContact.getCompanyId());
            throw new AccessDeniedException("User is not authorized to update this contact.");
        }

        log.info("Contact {} found and user authorized. Proceeding with update.", existingContact.getId());

        // Update fields
        existingContact.setName(contactDtoToUpdate.getName());
        existingContact.setContactNo(contactDtoToUpdate.getContactNo());
        existingContact.setEmail(contactDtoToUpdate.getEmail());
        existingContact.setCompanyName(contactDtoToUpdate.getCompanyName());

        ContactBook updatedContact = contactRepository.save(existingContact);
        log.info("Successfully updated contact with ID: {}", updatedContact.getId());

        return mapEntityToDto(updatedContact);
    }

    @Override
    @Transactional
    public DeleteContactResponseDto deleteContact(Integer contactId, Integer requestingUserCompanyId) {
        log.info("Attempting to delete contact with ID: {} by user with CompanyID: {}", contactId, requestingUserCompanyId);

        if (contactId == null) {
            log.warn("Delete failed: Contact ID is null.");
            return new DeleteContactResponseDto("Contact ID cannot be null for a delete operation.", false);
        }

        Optional<ContactBook> contactOptional = contactRepository.findById(contactId);
        if (contactOptional.isEmpty()) {
            log.warn("Delete failed: Contact not found with ID: {}", contactId);
            return new DeleteContactResponseDto("Contact not found with id: " + contactId, false);
        }
        ContactBook contactToDelete = contactOptional.get();

        // Authorization check
        if (contactToDelete.getCompanyId() == null || !contactToDelete.getCompanyId().equals(requestingUserCompanyId)) {
            log.warn("Authorization failed: User with CompanyID {} attempted to delete contact {} belonging to CompanyID {}.",
                    requestingUserCompanyId, contactToDelete.getId(), contactToDelete.getCompanyId());
            return new DeleteContactResponseDto("User is not authorized to delete this contact.", false);
        }

        log.info("Contact {} found and user authorized. Checking project dependencies.", contactToDelete.getId());

        try {
            boolean isInUseByProject = projectRepository.existsByContactPersonIdAndCompanyId(contactId, requestingUserCompanyId);
            if (isInUseByProject) {
                log.info("Delete failed: Contact {} is in use by a project for CompanyID {}.", contactId, requestingUserCompanyId);
                return new DeleteContactResponseDto("Not deleted, Record is used in Project.", false);
            }

            contactRepository.delete(contactToDelete);
            log.info("Successfully deleted contact with ID: {}", contactId);
            return new DeleteContactResponseDto("Record deleted", true);

        } catch (Exception e) {
            log.error("Error during contact deletion process for ID: {}: {}", contactId, e.getMessage(), e);
            return new DeleteContactResponseDto("An error occurred during deletion: " + e.getMessage(), false);
        }
    }

    @Override
    public ContactDto getContactByIdAndCompanyId(Integer contactId, Integer requestingUserCompanyId) {
        log.info("Attempting to retrieve contact with ID: {} for user with CompanyID: {}", contactId, requestingUserCompanyId);

        if (contactId == null) {
            log.warn("Get contact failed: Contact ID is null.");
            throw new IllegalArgumentException("Contact ID cannot be null.");
        }

        ContactBook contactBook = contactRepository.findById(contactId)
                .orElseThrow(() -> {
                    log.warn("Get contact failed: Contact not found with ID: {}", contactId);
                    return new EntityNotFoundException("Contact not found with id: " + contactId);
                });

        // Authorization check
        if (contactBook.getCompanyId() == null || !contactBook.getCompanyId().equals(requestingUserCompanyId)) {
            log.warn("Authorization failed: User with CompanyID {} attempted to access contact {} belonging to CompanyID {}.",
                    requestingUserCompanyId, contactBook.getId(), contactBook.getCompanyId());
            throw new AccessDeniedException("User is not authorized to access this contact.");
        }

        log.info("Contact {} found and user authorized. Returning DTO.", contactBook.getId());
        return mapEntityToDto(contactBook);
    }

    // --- Helper Mapping Methods --- //

    private ContactBook mapDtoToEntity(ContactDto dto) {
        return ContactBook.builder()
                // ID is not set here, it will be generated upon save
                .name(dto.getName())
                .contactNo(dto.getContactNo())
                .email(dto.getEmail())
                .companyName(dto.getCompanyName())
                // companyId is NOT mapped from dto here, it's set directly in createContact
                .build();
    }

    private ContactDto mapEntityToDto(ContactBook entity) {
        return ContactDto.builder()
                .id(entity.getId())
                .name(entity.getName())
                .contactNo(entity.getContactNo())
                .email(entity.getEmail())
                .companyName(entity.getCompanyName())
                .companyId(entity.getCompanyId())
                .build();
    }

    /**
     * 验证项目是否存在且属于指定的公司
     * 
     * @param projectId 项目ID
     * @param companyId 公司ID
     * @throws IllegalArgumentException 如果项目不存在或不属于该公司
     */
    private void validateProjectBelongsToCompany(Integer projectId, Integer companyId) {
        if (projectId == null || companyId == null) {
            throw new IllegalArgumentException("项目ID和公司ID不能为空");
        }
        
        boolean exists = projectRepository.existsByIdAndCompanyId(projectId, companyId);
        if (!exists) {
            log.warn("项目 {} 不存在或不属于公司 {}", projectId, companyId);
            throw new IllegalArgumentException("项目不存在或无权访问");
        }
    }
} 