package org.example.nbk.service;

import lombok.extern.slf4j.Slf4j;
import org.example.nbk.entity.*;
import org.example.nbk.repository.DocTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class DocTypeService {

    private final DocTypeRepository docTypeRepository;
    private CompanyWrapper dataCompany;

    @Autowired
    public DocTypeService(DocTypeRepository docTypeRepository) {
        this.docTypeRepository = docTypeRepository;
    }

    // Constructor with CompanyWrapper
    public DocTypeService(DocTypeRepository docTypeRepository, CompanyWrapper dataCompany) {
        this.docTypeRepository = docTypeRepository;
        this.dataCompany = dataCompany;
    }

    public WrapperDocType getSingleDocType(int id) {
        WrapperDocType data = new WrapperDocType();
        Optional<DocType> docTypeOpt = docTypeRepository.findById(id);
        
        if (docTypeOpt.isPresent()) {
            data.setDocType(docTypeOpt.get());
        } else {
            log.warn("Document type with id {} not found", id);
        }
        
        return data;
    }

    public WrapperMultiDocTypes getAllDocType() {
        WrapperMultiDocTypes data = new WrapperMultiDocTypes();
        List<DocType> docTypes = docTypeRepository.findByCompanyId(dataCompany.getCompanyID());
        data.setMultiDocTypes(docTypes);
        return data;
    }

    public WrapperDocType updateSingleDocType(DocType docType) {
        WrapperDocType data = new WrapperDocType();
        
        // Ensure the record exists before updating
        if (docTypeRepository.existsById(docType.getId())) {
            DocType updatedDocType = docTypeRepository.save(docType);
            data.setDocType(updatedDocType);
        } else {
            log.warn("Document type with id {} not found for update", docType.getId());
        }
        
        return data;
    }

    public WrapperDocType createSingleDocType(DocType docType) {
        WrapperDocType data = new WrapperDocType();
        
        // Set the company ID from the dataCompany
        docType.setCompanyId(dataCompany.getCompanyID());
        
        DocType createdDocType = docTypeRepository.save(docType);
        data.setDocType(createdDocType);
        
        return data;
    }

    public RequestResponse deleteSingleDocType(int id) {
        RequestResponse response = new RequestResponse();
        
        try {
            if (docTypeRepository.existsById(id)) {
                docTypeRepository.deleteById(id);
                response.setSuccess(true);
                response.setMessage("Record deleted");
                response.setUserProfileID(null);
                response.setDataCompany(null);
                response.setAdminUser(false);
            } else {
                response.setSuccess(false);
                response.setMessage("Document type not found");
                response.setUserProfileID(null);
                response.setDataCompany(null);
                response.setAdminUser(false);
                log.warn("Document type with id {} not found for deletion", id);
            }
        } catch (Exception ex) {
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
            response.setUserProfileID(null);
            response.setDataCompany(null);
            response.setAdminUser(false);
            log.error("Error deleting document type: {}", ex.getMessage(), ex);
        }
        
        return response;
    }
} 