package org.example.nbk.service;

import org.example.nbk.dto.RequestResponse;
import org.example.nbk.dto.workflow.WrapperProjectInvoiceDataDto;

public interface TripletexService {

    /**
     * Creates an invoice in the Tripletex system.
     * This involves finding/creating the customer, finding/creating products for each service,
     * and then creating the invoice itself.
     *
     * @param projectId The ID of the project to create an invoice for.
     * @return A RequestResponse indicating success or failure.
     */
    RequestResponse createTripletexOrderFromProject(Integer projectId);

    WrapperProjectInvoiceDataDto getInvoiceDetails(Integer projectId);

    RequestResponse sendInvoice(Integer projectId);
} 