package org.example.nbk.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.example.nbk.entity.CompanyWrapper;
import org.example.nbk.entity.RequestResponse;
import org.example.nbk.entity.User;
import org.example.nbk.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
public class AuthorizationService {

    private final UserRepository userRepository;
    private final JwtService jwtService;

    @Autowired
    public AuthorizationService(UserRepository userRepository, JwtService jwtService) {
        this.userRepository = userRepository;
        this.jwtService = jwtService;
    }

    public RequestResponse requestTokenAuth(HttpServletRequest request, int userTypeID) {
        RequestResponse response = new RequestResponse();
        
        try {
            // 从请求头中获取认证信息
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                response.setSuccess(false);
                response.setMessage("Missing or invalid Authorization header");
                return response;
            }
            
            // 提取 token
            String token = authHeader.substring(7);
            
            // 验证 token 并获取用户 ID
            if (!jwtService.validateToken(token)) {
                response.setSuccess(false);
                response.setMessage("TokenExpired");
                return response;
            }
            
            int userId = jwtService.getUserIdFromToken(token);
            response.setUserProfileID(userId);
            
            // 查询用户信息
            Optional<User> userOptional = userRepository.findById(userId);
            if (userOptional.isEmpty()) {
                response.setSuccess(false);
                response.setMessage("IncorrectToken");
                return response;
            }
            
            User user = userOptional.get();
            
            // 验证 token 是否匹配
            if (token == null || !token.equals(user.getToken())) {
                response.setSuccess(false);
                response.setMessage("IncorrectToken");
                return response;
            }
            
            // 验证 token 是否过期
            if (user.getTokenValidTo() == null || user.getTokenValidTo().isBefore(LocalDateTime.now())) {
                response.setSuccess(false);
                response.setMessage("TokenExpired");
                return response;
            }
            
            // 检查用户类型（如果指定）
            if (userTypeID == 2 && (user.getUserTypeID() == null || user.getUserTypeID() != 2)) {
                response.setSuccess(false);
                response.setMessage("UserNotAuthorizedToLogin");
                return response;
            }
            
            // 设置成功响应
            response.setSuccess(true);
            response.setAdminUser(Boolean.TRUE.equals(user.getIsAdmin()));
            
            // 设置公司信息
            CompanyWrapper companyWrapper = new CompanyWrapper();
            companyWrapper.setCompanyID(user.getCompanyID());
            companyWrapper.setSystemOwner(Boolean.TRUE.equals(user.getIsSystemOwner()));
            response.setDataCompany(companyWrapper);
            
            return response;
            
        } catch (Exception e) {
            log.error("Error authenticating request: {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("Authentication error: " + e.getMessage());
            return response;
        }
    }
} 