package org.example.nbk.repository;

import org.example.nbk.entity.User;
// import org.springframework.data.domain.Pageable; // Not used in findTop5ByOrderByIdAsc
import org.springframework.data.jpa.repository.JpaRepository;
// import org.springframework.data.jpa.repository.Query; // Not used for findTop5ByOrderByIdAsc
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional; // Needed for Optional<User>

@Repository
public interface UserRepository extends JpaRepository<User, Integer> {

    // JPA query method to get top 5 users, ordered by ID ascending
    List<User> findTop5ByOrderByIdAsc();

    // Find user by username
    Optional<User> findByUserName(String userName);
    
    // Find users by company ID and active status
    List<User> findByCompanyIDAndIsActive(Integer companyID, Boolean isActive);
    
    // Find users by company ID and user type IDs
    List<User> findByCompanyIDAndUserTypeIDIn(Integer companyID, List<Integer> userTypeIDs);

} 