package org.example.nbk.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * 缓存配置类，提供缓存相关的配置和键生成器
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 配置缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        // Specify the cache names that will be used in the application
        // This pre-creates them with the ConcurrentMapCacheManager
        cacheManager.setCacheNames(Arrays.asList(
                "partyDocs",
                "partyDocCounts",
                "partyUploadedFiles",
                "checklistTemplate",
                "checklistTemplateList",
                "checklistItemInspectionData",
                "projectCache",
                "projectCountsCache",
                "projectLeaderCache",
                "projectChecklists",
                "projectChecklistsCache", // 添加缺失的缓存名称
                "projectChecklist",
                "checklistItems",
                "projectPartyCache",
                "inspectorsNewFormatCache"
        ));
        // If you don't set storeByValue to true (default is false),
        // mutable objects returned from cache might be modified, affecting other users of the cache.
        // For DTOs that are effectively immutable or where this risk is low, false is fine and more performant.
        // cacheManager.setStoreByValue(true); // Consider if your cached objects are mutable
        return cacheManager;
    }
} 