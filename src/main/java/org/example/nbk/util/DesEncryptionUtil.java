package org.example.nbk.util;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class DesEncryptionUtil {

    private static final byte[] KEY_BYTES = new byte[]{1, 23, 13, 44, 5, 6, 7, 8};
    private static final byte[] IV_BYTES = new byte[]{1, 23, 13, 44, 5, 6, 7, 8};

    private static final String ALGORITHM = "DES";
    private static final String TRANSFORMATION = "DES/CBC/PKCS5Padding"; // Common for DES, ensure matches C# behavior

    public static String encrypt(String text) {
        if (text == null) {
            return null;
        }
        try {
            SecretKey secretKey = new SecretKeySpec(KEY_BYTES, ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(IV_BYTES);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParameterSpec);

            // C# Encoding.Unicode is UTF-16LE
            byte[] inputBuffer = text.getBytes(StandardCharsets.UTF_16LE);
            byte[] outputBuffer = cipher.doFinal(inputBuffer);
            return Base64.getEncoder().encodeToString(outputBuffer);
        } catch (Exception e) {
            // Log error appropriately or throw a custom exception
            // For now, rethrowing as a runtime exception to signal critical failure
            throw new RuntimeException("Error during DES encryption", e);
        }
    }

    // Decrypt method for testing or if needed, ensuring it matches C# logic
    public static String decrypt(String base64CipherText) {
        if (base64CipherText == null) {
            return null;
        }
        try {
            SecretKey secretKey = new SecretKeySpec(KEY_BYTES, ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(IV_BYTES);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);

            byte[] inputBuffer = Base64.getDecoder().decode(base64CipherText);
            byte[] outputBuffer = cipher.doFinal(inputBuffer);
            // C# Encoding.Unicode is UTF-16LE
            return new String(outputBuffer, StandardCharsets.UTF_16LE);
        } catch (Exception e) {
            throw new RuntimeException("Error during DES decryption", e);
        }
    }
} 