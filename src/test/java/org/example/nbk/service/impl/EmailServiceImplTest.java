package org.example.nbk.service.impl;

import org.example.nbk.dto.workflow.EmailProjectPartiesWorkflowDto;
import org.example.nbk.dto.workflow.EmailProjectPartiesWorkflowEntDto;
import org.example.nbk.dto.workflow.ProjectWorkflowDto;
import org.example.nbk.entity.EmailHistory;
import org.example.nbk.repository.EmailHistoryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import org.example.nbk.service.MailSenderService;

@ExtendWith(MockitoExtension.class)
class EmailServiceImplTest {

    @Mock
    private MailSenderService mailSenderService;

    @Mock
    private JavaMailSender mailSender;

    @Mock
    private EmailHistoryRepository emailHistoryRepository;

    @Mock
    private MimeMessage mimeMessage;

    @Mock
    private MultipartFile multipartFile;

    @InjectMocks
    private EmailServiceImpl emailService;

    @Captor
    private ArgumentCaptor<EmailHistory> emailHistoryCaptor;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(emailService, "defaultFromEmail", "<EMAIL>");
        lenient().when(mailSenderService.getJavaMailSender(any())).thenReturn(mailSender);
    }

    @Test
    void sendEmail_Success() throws MessagingException {
        // Given
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailService.sendEmail("<EMAIL>", "Test Subject", "<p>Test Content</p>");

        // Then
        assertTrue(result);
        verify(mailSender).createMimeMessage();
        verify(mailSender).send(mimeMessage);
    }

    @Test
    void sendEmail_WithNullParams_StillSends() throws MessagingException {
        // Given
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailService.sendEmail("<EMAIL>", null, null);

        // Then
        assertTrue(result);
        verify(mailSender).createMimeMessage();
        verify(mailSender).send(mimeMessage);
    }

    @Test
    void sendEmail_WithNullTo_ReturnsFalse() {
        // When
        boolean result = emailService.sendEmail(null, "Test Subject", "Test Content");

        // Then
        assertFalse(result);
        verify(mailSender, never()).createMimeMessage();
        verify(mailSender, never()).send(any(MimeMessage.class));
    }

    @Test
    void sendEmailWithAttachment_Success() throws MessagingException {
        // Given
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(mailSender).send(any(MimeMessage.class));
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getOriginalFilename()).thenReturn("test.pdf");

        // When
        boolean result = emailService.sendEmailWithAttachment(
                "<EMAIL>", "Test Subject", "<p>Test Content</p>", multipartFile);

        // Then
        assertTrue(result);
        verify(mailSender).createMimeMessage();
        verify(mailSender).send(mimeMessage);
    }

    @Test
    void sendEmailWithAttachments_Success() throws MessagingException {
        // Given
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(mailSender).send(any(MimeMessage.class));
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getOriginalFilename()).thenReturn("test.pdf");
        List<MultipartFile> files = Collections.singletonList(multipartFile);

        // When
        boolean result = emailService.sendEmailWithAttachments(
                "<EMAIL>", "Test Subject", "<p>Test Content</p>", files);

        // Then
        assertTrue(result);
        verify(mailSender).createMimeMessage();
        verify(mailSender).send(mimeMessage);
    }

    @Test
    void saveEmailHistory_Success() {
        // Given
        ProjectWorkflowDto projectWorkflow = new ProjectWorkflowDto();
        projectWorkflow.setProjectId(1);
        projectWorkflow.setWorkflowId(2);
        projectWorkflow.setWorkflowStepId(3);
        projectWorkflow.setEmailSubject("Test Subject");
        projectWorkflow.setEmailContent("<p>Test Content</p>");
        projectWorkflow.setToEmail("<EMAIL>");
        projectWorkflow.setFromEmail("<EMAIL>");

        EmailProjectPartiesWorkflowDto emailProjectPartiesWorkflowDto = new EmailProjectPartiesWorkflowDto();
        emailProjectPartiesWorkflowDto.setPartyId(4);
        List<EmailProjectPartiesWorkflowEntDto> emailList = new ArrayList<>();
        EmailProjectPartiesWorkflowEntDto party = new EmailProjectPartiesWorkflowEntDto();
        party.setPartyTypeID(5);
        emailList.add(party);
        emailProjectPartiesWorkflowDto.setEmailProjectPartiesWorkflowList(emailList);
        projectWorkflow.setEmailProjectParties(emailProjectPartiesWorkflowDto);

        when(emailHistoryRepository.save(any(EmailHistory.class))).thenReturn(new EmailHistory());

        // When
        boolean result = emailService.saveEmailHistory(projectWorkflow);

        // Then
        assertTrue(result);
        verify(emailHistoryRepository).save(emailHistoryCaptor.capture());
        EmailHistory savedHistory = emailHistoryCaptor.getValue();
        assertEquals(1, savedHistory.getProjectId());
        assertEquals(2, savedHistory.getWorkflowId());
        assertEquals(3, savedHistory.getWorkflowStepId());
        assertEquals("Test Subject", savedHistory.getSubject());
        assertEquals("<p>Test Content</p>", savedHistory.getMessage());
        assertEquals("<EMAIL>", savedHistory.getToEmail());
        assertEquals("<EMAIL>", savedHistory.getFromEmail());
        assertEquals(4, savedHistory.getPartyId());
        assertEquals(5, savedHistory.getPartyTypeId());
        assertEquals(1, savedHistory.getCompanyId());
        assertTrue(savedHistory.getStatus());
        assertTrue(savedHistory.getIsEmail());
    }

    @Test
    void saveEmailHistory_WithNullProjectWorkflow_ReturnsFalse() {
        // When
        boolean result = emailService.saveEmailHistory(null);

        // Then
        assertFalse(result);
        verify(emailHistoryRepository, never()).save(any());
    }
} 